server:
  port: 8080

spring:
  application:
    name: social-network-graph
  
  # Database Configuration
  datasource:
    url: jdbc:h2:mem:socialdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Redis Configuration
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      jedis:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # Batch Configuration
  batch:
    job:
      enabled: false
    jdbc:
      initialize-schema: always

# Management and Metrics
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Application Specific Configuration
social-network:
  recommendations:
    max-results: 10
    cache-ttl: 3600 # 1 hour in seconds
    batch-size: 1000
  graph:
    max-depth: 2 # for BFS traversal
  cache:
    user-subgraph-ttl: 1800 # 30 minutes
    recommendation-ttl: 3600 # 1 hour

logging:
  level:
    edu.social: DEBUG
    org.springframework.batch: INFO
    org.springframework.cache: DEBUG
