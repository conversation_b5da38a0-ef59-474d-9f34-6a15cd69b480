package edu.social.service;

import edu.social.model.RecommendationScore;
import edu.social.model.User;
import edu.social.repository.UserRepository;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for generating friend recommendations using graph algorithms
 * and similarity metrics with min-heap for top-k selection
 */
@Service
public class RecommendationService {
    
    private static final Logger logger = LoggerFactory.getLogger(RecommendationService.class);
    
    @Autowired
    private SocialGraphService socialGraphService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @Value("${social-network.recommendations.max-results:10}")
    private int maxRecommendations;
    
    /**
     * Generate friend recommendations for a user using combined similarity metrics
     */
    @Cacheable(value = "recommendations", key = "#userId")
    public List<RecommendationScore> generateRecommendations(Long userId) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            logger.info("Generating recommendations for user {}", userId);
            
            // Ensure graph is built
            if (socialGraphService.needsGraphRebuild()) {
                socialGraphService.buildGraph();
            }
            
            // Get two-hop neighborhood (friends of friends)
            Set<Long> candidates = socialGraphService.getTwoHopNeighborhood(userId);
            
            // Remove users who are already friends
            Set<Long> directFriends = socialGraphService.getDirectFriends(userId);
            candidates.removeAll(directFriends);
            candidates.remove(userId); // Remove self
            
            if (candidates.isEmpty()) {
                logger.info("No recommendation candidates found for user {}", userId);
                return new ArrayList<>();
            }
            
            // Use min-heap to efficiently get top-k recommendations
            PriorityQueue<RecommendationScore> minHeap = new PriorityQueue<>(
                maxRecommendations + 1, 
                Comparator.reverseOrder() // Min-heap based on combined score
            );
            
            // Load candidate users
            List<User> candidateUsers = userRepository.findActiveUsersByIds(candidates);
            Map<Long, User> userMap = candidateUsers.stream()
                    .collect(Collectors.toMap(User::getId, user -> user));
            
            // Calculate scores for each candidate
            for (Long candidateId : candidates) {
                User candidateUser = userMap.get(candidateId);
                if (candidateUser == null) continue;
                
                RecommendationScore score = calculateRecommendationScore(userId, candidateUser);
                
                if (minHeap.size() < maxRecommendations) {
                    minHeap.offer(score);
                } else if (score.getCombinedScore() > minHeap.peek().getCombinedScore()) {
                    minHeap.poll(); // Remove lowest score
                    minHeap.offer(score);
                }
            }
            
            // Convert min-heap to sorted list (highest scores first)
            List<RecommendationScore> recommendations = new ArrayList<>(minHeap);
            recommendations.sort(Comparator.reverseOrder());
            
            logger.info("Generated {} recommendations for user {}", recommendations.size(), userId);
            return recommendations;
            
        } finally {
            sample.stop(Timer.builder("recommendation.generation.time")
                    .description("Time taken to generate recommendations")
                    .register(meterRegistry));
        }
    }
    
    /**
     * Calculate recommendation score for a candidate user
     */
    private RecommendationScore calculateRecommendationScore(Long userId, User candidate) {
        Long candidateId = candidate.getId();
        
        // Calculate Jaccard similarity
        double jaccardScore = socialGraphService.calculateJaccardSimilarity(userId, candidateId);
        
        // Calculate Adamic-Adar index
        double adamicAdarScore = socialGraphService.calculateAdamicAdarIndex(userId, candidateId);
        
        // Get mutual friends count
        Set<Long> mutualFriends = socialGraphService.getMutualFriends(userId, candidateId);
        int mutualFriendsCount = mutualFriends.size();
        
        return new RecommendationScore(
            candidate, 
            jaccardScore, 
            adamicAdarScore, 
            mutualFriendsCount,
            "COMBINED_SIMILARITY"
        );
    }
    
    /**
     * Generate recommendations using only Jaccard similarity
     */
    public List<RecommendationScore> generateJaccardRecommendations(Long userId) {
        return generateRecommendationsWithAlgorithm(userId, "JACCARD", 
            (uid, cid) -> socialGraphService.calculateJaccardSimilarity(uid, cid));
    }
    
    /**
     * Generate recommendations using only Adamic-Adar index
     */
    public List<RecommendationScore> generateAdamicAdarRecommendations(Long userId) {
        return generateRecommendationsWithAlgorithm(userId, "ADAMIC_ADAR", 
            (uid, cid) -> socialGraphService.calculateAdamicAdarIndex(uid, cid));
    }
    
    /**
     * Generic method for generating recommendations with a specific algorithm
     */
    private List<RecommendationScore> generateRecommendationsWithAlgorithm(
            Long userId, String algorithm, SimilarityCalculator calculator) {
        
        if (socialGraphService.needsGraphRebuild()) {
            socialGraphService.buildGraph();
        }
        
        Set<Long> candidates = socialGraphService.getTwoHopNeighborhood(userId);
        Set<Long> directFriends = socialGraphService.getDirectFriends(userId);
        candidates.removeAll(directFriends);
        candidates.remove(userId);
        
        if (candidates.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<User> candidateUsers = userRepository.findActiveUsersByIds(candidates);
        
        return candidateUsers.stream()
                .map(candidate -> {
                    double score = calculator.calculate(userId, candidate.getId());
                    Set<Long> mutualFriends = socialGraphService.getMutualFriends(userId, candidate.getId());
                    
                    return new RecommendationScore(
                        candidate,
                        algorithm.equals("JACCARD") ? score : 0.0,
                        algorithm.equals("ADAMIC_ADAR") ? score : 0.0,
                        mutualFriends.size(),
                        algorithm
                    );
                })
                .sorted(Comparator.reverseOrder())
                .limit(maxRecommendations)
                .collect(Collectors.toList());
    }
    
    /**
     * Get mutual friends for display purposes
     */
    public List<User> getMutualFriends(Long userId1, Long userId2) {
        Set<Long> mutualFriendIds = socialGraphService.getMutualFriends(userId1, userId2);
        return userRepository.findActiveUsersByIds(mutualFriendIds);
    }
    
    /**
     * Get recommendation statistics
     */
    public Map<String, Object> getRecommendationStatistics(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        
        Set<Long> directFriends = socialGraphService.getDirectFriends(userId);
        Set<Long> twoHopNeighbors = socialGraphService.getTwoHopNeighborhood(userId);
        
        stats.put("directFriendsCount", directFriends.size());
        stats.put("twoHopNeighborsCount", twoHopNeighbors.size());
        stats.put("potentialRecommendations", twoHopNeighbors.size() - directFriends.size());
        
        return stats;
    }
    
    @FunctionalInterface
    private interface SimilarityCalculator {
        double calculate(Long userId1, Long userId2);
    }
}
