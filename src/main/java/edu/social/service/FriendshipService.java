package edu.social.service;

import edu.social.model.Friendship;
import edu.social.model.User;
import edu.social.repository.FriendshipRepository;
import edu.social.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing friendships in the social network
 */
@Service
@Transactional
public class FriendshipService {
    
    private static final Logger logger = LoggerFactory.getLogger(FriendshipService.class);
    
    @Autowired
    private FriendshipRepository friendshipRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private SocialGraphService socialGraphService;
    
    /**
     * Create a friendship between two users
     */
    @CacheEvict(value = {"userFriends", "recommendations"}, allEntries = true)
    public Friendship createFriendship(Long userId1, Long userId2) {
        logger.info("Creating friendship between users {} and {}", userId1, userId2);
        
        if (userId1.equals(userId2)) {
            throw new IllegalArgumentException("User cannot be friends with themselves");
        }
        
        Optional<User> user1 = userRepository.findById(userId1);
        Optional<User> user2 = userRepository.findById(userId2);
        
        if (user1.isEmpty() || user2.isEmpty()) {
            throw new IllegalArgumentException("One or both users not found");
        }
        
        if (!user1.get().getIsActive() || !user2.get().getIsActive()) {
            throw new IllegalArgumentException("Cannot create friendship with inactive user");
        }
        
        // Check if friendship already exists
        Optional<Friendship> existingFriendship = friendshipRepository.findFriendshipBetweenUsers(
            user1.get(), user2.get(), Friendship.FriendshipStatus.ACTIVE);
        
        if (existingFriendship.isPresent()) {
            throw new IllegalArgumentException("Friendship already exists between these users");
        }
        
        // Create bidirectional friendship records
        Friendship friendship1 = new Friendship(user1.get(), user2.get(), Friendship.FriendshipStatus.ACTIVE);
        Friendship friendship2 = new Friendship(user2.get(), user1.get(), Friendship.FriendshipStatus.ACTIVE);
        
        friendshipRepository.save(friendship1);
        friendshipRepository.save(friendship2);
        
        // Update JPA relationship
        user1.get().addFriend(user2.get());
        userRepository.save(user1.get());
        
        // Update graph
        socialGraphService.addFriendship(userId1, userId2);
        
        logger.info("Successfully created friendship between users {} and {}", userId1, userId2);
        return friendship1;
    }
    
    /**
     * Remove friendship between two users
     */
    @CacheEvict(value = {"userFriends", "recommendations"}, allEntries = true)
    public void removeFriendship(Long userId1, Long userId2) {
        logger.info("Removing friendship between users {} and {}", userId1, userId2);
        
        Optional<User> user1 = userRepository.findById(userId1);
        Optional<User> user2 = userRepository.findById(userId2);
        
        if (user1.isEmpty() || user2.isEmpty()) {
            throw new IllegalArgumentException("One or both users not found");
        }
        
        // Find and remove both directions of friendship
        Optional<Friendship> friendship1 = friendshipRepository.findByUserAndFriend(user1.get(), user2.get());
        Optional<Friendship> friendship2 = friendshipRepository.findByUserAndFriend(user2.get(), user1.get());
        
        friendship1.ifPresent(friendshipRepository::delete);
        friendship2.ifPresent(friendshipRepository::delete);
        
        // Update JPA relationship
        user1.get().removeFriend(user2.get());
        userRepository.save(user1.get());
        
        // Update graph
        socialGraphService.removeFriendship(userId1, userId2);
        
        logger.info("Successfully removed friendship between users {} and {}", userId1, userId2);
    }
    
    /**
     * Block a user (set friendship status to BLOCKED)
     */
    @CacheEvict(value = {"userFriends", "recommendations"}, allEntries = true)
    public void blockUser(Long userId, Long userToBlockId) {
        logger.info("User {} blocking user {}", userId, userToBlockId);
        
        Optional<User> user = userRepository.findById(userId);
        Optional<User> userToBlock = userRepository.findById(userToBlockId);
        
        if (user.isEmpty() || userToBlock.isEmpty()) {
            throw new IllegalArgumentException("One or both users not found");
        }
        
        // Remove existing friendship if it exists
        removeFriendship(userId, userToBlockId);
        
        // Create blocked relationship
        Friendship blockedFriendship = new Friendship(user.get(), userToBlock.get(), Friendship.FriendshipStatus.BLOCKED);
        friendshipRepository.save(blockedFriendship);
        
        logger.info("User {} successfully blocked user {}", userId, userToBlockId);
    }
    
    /**
     * Unblock a user
     */
    @CacheEvict(value = {"userFriends", "recommendations"}, allEntries = true)
    public void unblockUser(Long userId, Long userToUnblockId) {
        logger.info("User {} unblocking user {}", userId, userToUnblockId);
        
        Optional<User> user = userRepository.findById(userId);
        Optional<User> userToUnblock = userRepository.findById(userToUnblockId);
        
        if (user.isEmpty() || userToUnblock.isEmpty()) {
            throw new IllegalArgumentException("One or both users not found");
        }
        
        // Find and remove blocked relationship
        Optional<Friendship> blockedFriendship = friendshipRepository.findByUserAndFriend(user.get(), userToUnblock.get());
        
        if (blockedFriendship.isPresent() && 
            blockedFriendship.get().getStatus() == Friendship.FriendshipStatus.BLOCKED) {
            friendshipRepository.delete(blockedFriendship.get());
            logger.info("User {} successfully unblocked user {}", userId, userToUnblockId);
        } else {
            throw new IllegalArgumentException("No blocked relationship found between these users");
        }
    }
    
    /**
     * Check if two users are friends
     */
    public boolean areFriends(Long userId1, Long userId2) {
        Optional<User> user1 = userRepository.findById(userId1);
        Optional<User> user2 = userRepository.findById(userId2);
        
        if (user1.isEmpty() || user2.isEmpty()) {
            return false;
        }
        
        return friendshipRepository.findFriendshipBetweenUsers(
            user1.get(), user2.get(), Friendship.FriendshipStatus.ACTIVE).isPresent();
    }
    
    /**
     * Check if a user is blocked by another user
     */
    public boolean isBlocked(Long userId, Long potentialBlockedUserId) {
        Optional<User> user = userRepository.findById(userId);
        Optional<User> potentialBlockedUser = userRepository.findById(potentialBlockedUserId);
        
        if (user.isEmpty() || potentialBlockedUser.isEmpty()) {
            return false;
        }
        
        return friendshipRepository.existsByUserAndFriendAndStatus(
            user.get(), potentialBlockedUser.get(), Friendship.FriendshipStatus.BLOCKED);
    }
    
    /**
     * Get all active friends of a user
     */
    public List<User> getActiveFriends(Long userId) {
        Optional<User> user = userRepository.findById(userId);
        if (user.isEmpty()) {
            throw new IllegalArgumentException("User not found: " + userId);
        }
        
        return friendshipRepository.findActiveFriendsByUser(user.get());
    }
    
    /**
     * Get friendship count for a user
     */
    public long getFriendshipCount(Long userId) {
        Optional<User> user = userRepository.findById(userId);
        if (user.isEmpty()) {
            throw new IllegalArgumentException("User not found: " + userId);
        }
        
        return friendshipRepository.countActiveFriendshipsByUser(user.get());
    }
    
    /**
     * Get all friendships for a user (including blocked)
     */
    public List<Friendship> getAllFriendships(Long userId) {
        Optional<User> user = userRepository.findById(userId);
        if (user.isEmpty()) {
            throw new IllegalArgumentException("User not found: " + userId);
        }
        
        return friendshipRepository.findAllFriendshipsByUser(user.get());
    }
}
