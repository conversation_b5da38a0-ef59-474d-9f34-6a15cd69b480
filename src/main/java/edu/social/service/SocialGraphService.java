package edu.social.service;

import edu.social.model.User;
import edu.social.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Service for managing the social graph using adjacency list representation
 * and implementing BFS for neighborhood discovery
 */
@Service
public class SocialGraphService {
    
    private static final Logger logger = LoggerFactory.getLogger(SocialGraphService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    // In-memory adjacency list for fast graph operations
    private final Map<Long, Set<Long>> adjacencyList = new ConcurrentHashMap<>();
    
    /**
     * Build adjacency list representation of the social graph
     */
    public void buildGraph() {
        logger.info("Building social graph adjacency list...");
        
        adjacencyList.clear();
        List<User> allUsers = userRepository.findByIsActiveTrue();
        
        for (User user : allUsers) {
            Set<Long> friendIds = user.getFriends().stream()
                    .filter(User::getIsActive)
                    .map(User::getId)
                    .collect(Collectors.toSet());
            
            adjacencyList.put(user.getId(), friendIds);
        }
        
        logger.info("Graph built with {} users and {} total connections", 
                   adjacencyList.size(), 
                   adjacencyList.values().stream().mapToInt(Set::size).sum() / 2);
    }
    
    /**
     * Get direct friends of a user
     */
    @Cacheable(value = "userFriends", key = "#userId")
    public Set<Long> getDirectFriends(Long userId) {
        return adjacencyList.getOrDefault(userId, new HashSet<>());
    }
    
    /**
     * Perform BFS to find two-hop neighborhood (friends of friends)
     * Returns users at exactly distance 2 from the source user
     */
    public Set<Long> getTwoHopNeighborhood(Long userId) {
        logger.debug("Computing two-hop neighborhood for user {}", userId);
        
        Set<Long> visited = new HashSet<>();
        Set<Long> twoHopNeighbors = new HashSet<>();
        Queue<Long> queue = new LinkedList<>();
        Map<Long, Integer> distances = new HashMap<>();
        
        // Initialize BFS
        queue.offer(userId);
        visited.add(userId);
        distances.put(userId, 0);
        
        while (!queue.isEmpty()) {
            Long currentUserId = queue.poll();
            int currentDistance = distances.get(currentUserId);
            
            // We only care about distances 0, 1, and 2
            if (currentDistance >= 2) {
                continue;
            }
            
            Set<Long> neighbors = getDirectFriends(currentUserId);
            
            for (Long neighborId : neighbors) {
                if (!visited.contains(neighborId)) {
                    visited.add(neighborId);
                    int newDistance = currentDistance + 1;
                    distances.put(neighborId, newDistance);
                    
                    if (newDistance == 2) {
                        // This is a friend of friend (two-hop neighbor)
                        twoHopNeighbors.add(neighborId);
                    } else if (newDistance < 2) {
                        // Continue BFS for distance < 2
                        queue.offer(neighborId);
                    }
                }
            }
        }
        
        logger.debug("Found {} two-hop neighbors for user {}", twoHopNeighbors.size(), userId);
        return twoHopNeighbors;
    }
    
    /**
     * Get mutual friends between two users
     */
    public Set<Long> getMutualFriends(Long userId1, Long userId2) {
        Set<Long> friends1 = getDirectFriends(userId1);
        Set<Long> friends2 = getDirectFriends(userId2);
        
        Set<Long> mutualFriends = new HashSet<>(friends1);
        mutualFriends.retainAll(friends2);
        
        return mutualFriends;
    }
    
    /**
     * Calculate Jaccard similarity between two users based on their friend networks
     * Jaccard = |intersection| / |union|
     */
    public double calculateJaccardSimilarity(Long userId1, Long userId2) {
        Set<Long> friends1 = getDirectFriends(userId1);
        Set<Long> friends2 = getDirectFriends(userId2);
        
        if (friends1.isEmpty() && friends2.isEmpty()) {
            return 0.0;
        }
        
        Set<Long> intersection = new HashSet<>(friends1);
        intersection.retainAll(friends2);
        
        Set<Long> union = new HashSet<>(friends1);
        union.addAll(friends2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    /**
     * Calculate Adamic-Adar index between two users
     * AA = sum(1 / log(degree(mutual_friend))) for all mutual friends
     */
    public double calculateAdamicAdarIndex(Long userId1, Long userId2) {
        Set<Long> mutualFriends = getMutualFriends(userId1, userId2);
        
        if (mutualFriends.isEmpty()) {
            return 0.0;
        }
        
        double adamicAdarScore = 0.0;
        
        for (Long mutualFriendId : mutualFriends) {
            int degree = getDirectFriends(mutualFriendId).size();
            if (degree > 1) { // Avoid log(1) = 0 and division by 0
                adamicAdarScore += 1.0 / Math.log(degree);
            }
        }
        
        return adamicAdarScore;
    }
    
    /**
     * Check if the graph needs to be rebuilt (called periodically)
     */
    public boolean needsGraphRebuild() {
        // Simple heuristic: rebuild if adjacency list is empty or significantly outdated
        return adjacencyList.isEmpty();
    }
    
    /**
     * Get graph statistics
     */
    public Map<String, Object> getGraphStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalUsers", adjacencyList.size());
        stats.put("totalConnections", adjacencyList.values().stream().mapToInt(Set::size).sum() / 2);
        
        if (!adjacencyList.isEmpty()) {
            double avgDegree = adjacencyList.values().stream()
                    .mapToInt(Set::size)
                    .average()
                    .orElse(0.0);
            stats.put("averageDegree", avgDegree);
            
            int maxDegree = adjacencyList.values().stream()
                    .mapToInt(Set::size)
                    .max()
                    .orElse(0);
            stats.put("maxDegree", maxDegree);
        }
        
        return stats;
    }
    
    /**
     * Add friendship to the graph
     */
    public void addFriendship(Long userId1, Long userId2) {
        adjacencyList.computeIfAbsent(userId1, k -> new HashSet<>()).add(userId2);
        adjacencyList.computeIfAbsent(userId2, k -> new HashSet<>()).add(userId1);
        logger.debug("Added friendship between users {} and {}", userId1, userId2);
    }
    
    /**
     * Remove friendship from the graph
     */
    public void removeFriendship(Long userId1, Long userId2) {
        adjacencyList.getOrDefault(userId1, new HashSet<>()).remove(userId2);
        adjacencyList.getOrDefault(userId2, new HashSet<>()).remove(userId1);
        logger.debug("Removed friendship between users {} and {}", userId1, userId2);
    }
}
