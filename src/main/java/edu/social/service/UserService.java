package edu.social.service;

import edu.social.model.User;
import edu.social.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing users in the social network
 */
@Service
@Transactional
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private SocialGraphService socialGraphService;
    
    /**
     * Create a new user
     */
    public User createUser(String username, String email, String fullName) {
        logger.info("Creating new user: {}", username);
        
        // Check if username or email already exists
        if (userRepository.existsByUsername(username)) {
            throw new IllegalArgumentException("Username already exists: " + username);
        }
        
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("Email already exists: " + email);
        }
        
        User user = new User(username, email, fullName);
        User savedUser = userRepository.save(user);
        
        // Rebuild graph to include new user
        socialGraphService.buildGraph();
        
        logger.info("Created user with ID: {}", savedUser.getId());
        return savedUser;
    }
    
    /**
     * Get user by ID
     */
    @Cacheable(value = "users", key = "#userId")
    public Optional<User> getUserById(Long userId) {
        return userRepository.findById(userId);
    }
    
    /**
     * Get user by username
     */
    @Cacheable(value = "users", key = "#username")
    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    /**
     * Get user by email
     */
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    /**
     * Get all active users
     */
    public List<User> getAllActiveUsers() {
        return userRepository.findByIsActiveTrue();
    }
    
    /**
     * Search users by username or full name
     */
    public List<User> searchUsers(String searchTerm) {
        logger.debug("Searching users with term: {}", searchTerm);
        return userRepository.searchUsers(searchTerm);
    }
    
    /**
     * Update user information
     */
    @CacheEvict(value = "users", key = "#user.id")
    public User updateUser(User user) {
        logger.info("Updating user: {}", user.getId());
        
        Optional<User> existingUser = userRepository.findById(user.getId());
        if (existingUser.isEmpty()) {
            throw new IllegalArgumentException("User not found: " + user.getId());
        }
        
        User updated = existingUser.get();
        updated.setFullName(user.getFullName());
        updated.setEmail(user.getEmail());
        
        return userRepository.save(updated);
    }
    
    /**
     * Deactivate user (soft delete)
     */
    @CacheEvict(value = "users", key = "#userId")
    public void deactivateUser(Long userId) {
        logger.info("Deactivating user: {}", userId);
        
        Optional<User> user = userRepository.findById(userId);
        if (user.isPresent()) {
            User userToDeactivate = user.get();
            userToDeactivate.setIsActive(false);
            userRepository.save(userToDeactivate);
            
            // Rebuild graph to remove deactivated user
            socialGraphService.buildGraph();
        }
    }
    
    /**
     * Activate user
     */
    @CacheEvict(value = "users", key = "#userId")
    public void activateUser(Long userId) {
        logger.info("Activating user: {}", userId);
        
        Optional<User> user = userRepository.findById(userId);
        if (user.isPresent()) {
            User userToActivate = user.get();
            userToActivate.setIsActive(true);
            userRepository.save(userToActivate);
            
            // Rebuild graph to include activated user
            socialGraphService.buildGraph();
        }
    }
    
    /**
     * Get user's friends
     */
    public List<User> getUserFriends(Long userId) {
        return userRepository.findFriendsByUserId(userId);
    }
    
    /**
     * Get user's friend count
     */
    public long getUserFriendCount(Long userId) {
        return userRepository.countFriendsByUserId(userId);
    }
    
    /**
     * Get users who are not friends with the given user
     */
    public List<User> getNonFriends(Long userId) {
        return userRepository.findNonFriends(userId);
    }
    
    /**
     * Check if user exists and is active
     */
    public boolean isUserActiveById(Long userId) {
        return userRepository.findById(userId)
                .map(User::getIsActive)
                .orElse(false);
    }
    
    /**
     * Get user statistics
     */
    public UserStatistics getUserStatistics(Long userId) {
        Optional<User> user = getUserById(userId);
        if (user.isEmpty()) {
            throw new IllegalArgumentException("User not found: " + userId);
        }
        
        long friendCount = getUserFriendCount(userId);
        
        return new UserStatistics(
            user.get(),
            friendCount,
            socialGraphService.getTwoHopNeighborhood(userId).size()
        );
    }
    
    /**
     * Inner class for user statistics
     */
    public static class UserStatistics {
        private final User user;
        private final long friendCount;
        private final long twoHopNeighborCount;
        
        public UserStatistics(User user, long friendCount, long twoHopNeighborCount) {
            this.user = user;
            this.friendCount = friendCount;
            this.twoHopNeighborCount = twoHopNeighborCount;
        }
        
        public User getUser() { return user; }
        public long getFriendCount() { return friendCount; }
        public long getTwoHopNeighborCount() { return twoHopNeighborCount; }
    }
}
