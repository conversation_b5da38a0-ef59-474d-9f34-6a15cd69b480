package edu.social.repository;

import edu.social.model.Friendship;
import edu.social.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FriendshipRepository extends JpaRepository<Friendship, Long> {
    
    Optional<Friendship> findByUserAndFriend(User user, User friend);
    
    List<Friendship> findByUserAndStatus(User user, Friendship.FriendshipStatus status);
    
    List<Friendship> findByFriendAndStatus(User friend, Friendship.FriendshipStatus status);
    
    @Query("SELECT f FROM Friendship f WHERE " +
           "((f.user = :user1 AND f.friend = :user2) OR (f.user = :user2 AND f.friend = :user1)) " +
           "AND f.status = :status")
    Optional<Friendship> findFriendshipBetweenUsers(@Param("user1") User user1, 
                                                   @Param("user2") User user2,
                                                   @Param("status") Friendship.FriendshipStatus status);
    
    @Query("SELECT COUNT(f) FROM Friendship f WHERE f.user = :user AND f.status = 'ACTIVE'")
    long countActiveFriendshipsByUser(@Param("user") User user);
    
    @Query("SELECT f.friend FROM Friendship f WHERE f.user = :user AND f.status = 'ACTIVE'")
    List<User> findActiveFriendsByUser(@Param("user") User user);
    
    @Query("SELECT f FROM Friendship f WHERE f.user = :user OR f.friend = :user")
    List<Friendship> findAllFriendshipsByUser(@Param("user") User user);
    
    boolean existsByUserAndFriendAndStatus(User user, User friend, Friendship.FriendshipStatus status);
}
