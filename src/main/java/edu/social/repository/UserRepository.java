package edu.social.repository;

import edu.social.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Optional<User> findByUsername(String username);
    
    Optional<User> findByEmail(String email);
    
    List<User> findByIsActiveTrue();
    
    boolean existsByUsername(String username);
    
    boolean existsByEmail(String email);
    
    @Query("SELECT u FROM User u WHERE u.isActive = true AND u.id IN :userIds")
    List<User> findActiveUsersByIds(@Param("userIds") Set<Long> userIds);
    
    @Query("SELECT u FROM User u WHERE u.isActive = true AND " +
           "(LOWER(u.username) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.fullName) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    List<User> searchUsers(@Param("searchTerm") String searchTerm);
    
    @Query("SELECT f FROM User u JOIN u.friends f WHERE u.id = :userId AND f.isActive = true")
    List<User> findFriendsByUserId(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(f) FROM User u JOIN u.friends f WHERE u.id = :userId AND f.isActive = true")
    long countFriendsByUserId(@Param("userId") Long userId);
    
    @Query("SELECT u FROM User u WHERE u.isActive = true AND u.id != :userId AND u NOT IN " +
           "(SELECT f FROM User user JOIN user.friends f WHERE user.id = :userId)")
    List<User> findNonFriends(@Param("userId") Long userId);
}
