package edu.social.batch;

import edu.social.model.RecommendationScore;
import edu.social.model.User;
import edu.social.service.RecommendationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Batch processor for precomputing friend recommendations
 */
@Component
public class RecommendationBatchProcessor implements ItemProcessor<User, User> {
    
    private static final Logger logger = LoggerFactory.getLogger(RecommendationBatchProcessor.class);
    
    @Autowired
    private RecommendationService recommendationService;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Override
    public User process(User user) throws Exception {
        try {
            logger.debug("Processing recommendations for user: {} (ID: {})", user.getUsername(), user.getId());
            
            // Generate recommendations for the user
            List<RecommendationScore> recommendations = recommendationService.generateRecommendations(user.getId());
            
            // Cache the recommendations
            if (cacheManager.getCache("recommendations") != null) {
                cacheManager.getCache("recommendations").put(user.getId(), recommendations);
                logger.debug("Cached {} recommendations for user: {}", recommendations.size(), user.getUsername());
            }
            
            // Log statistics
            if (!recommendations.isEmpty()) {
                double avgScore = recommendations.stream()
                        .mapToDouble(RecommendationScore::getCombinedScore)
                        .average()
                        .orElse(0.0);
                
                logger.info("Generated {} recommendations for user {} with average score: {:.4f}", 
                           recommendations.size(), user.getUsername(), avgScore);
            } else {
                logger.info("No recommendations generated for user: {}", user.getUsername());
            }
            
            return user;
            
        } catch (Exception e) {
            logger.error("Error processing recommendations for user: {} (ID: {})", 
                        user.getUsername(), user.getId(), e);
            throw e;
        }
    }
}
