package edu.social.model;

import java.io.Serializable;
import java.util.Objects;

/**
 * Represents a friend recommendation with scoring information
 */
public class RecommendationScore implements Comparable<RecommendationScore>, Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private final User recommendedUser;
    private final double jaccardScore;
    private final double adamicAdarScore;
    private final double combinedScore;
    private final int mutualFriendsCount;
    private final String algorithm;
    
    public RecommendationScore(User recommendedUser, double jaccardScore, double adamicAdarScore, 
                             int mutualFriendsCount, String algorithm) {
        this.recommendedUser = recommendedUser;
        this.jaccardScore = jaccardScore;
        this.adamicAdarScore = adamicAdarScore;
        this.mutualFriendsCount = mutualFriendsCount;
        this.algorithm = algorithm;
        
        // Combined score: weighted average of Jaccard and Adamic-Adar
        this.combinedScore = (0.4 * jaccardScore) + (0.6 * adamicAdarScore);
    }
    
    // Getters
    public User getRecommendedUser() {
        return recommendedUser;
    }
    
    public double getJaccardScore() {
        return jaccardScore;
    }
    
    public double getAdamicAdarScore() {
        return adamicAdarScore;
    }
    
    public double getCombinedScore() {
        return combinedScore;
    }
    
    public int getMutualFriendsCount() {
        return mutualFriendsCount;
    }
    
    public String getAlgorithm() {
        return algorithm;
    }
    
    @Override
    public int compareTo(RecommendationScore other) {
        // Higher scores should come first (descending order)
        int scoreComparison = Double.compare(other.combinedScore, this.combinedScore);
        if (scoreComparison != 0) {
            return scoreComparison;
        }
        
        // If scores are equal, prefer higher mutual friends count
        int mutualComparison = Integer.compare(other.mutualFriendsCount, this.mutualFriendsCount);
        if (mutualComparison != 0) {
            return mutualComparison;
        }
        
        // Finally, sort by user ID for consistency
        return Long.compare(this.recommendedUser.getId(), other.recommendedUser.getId());
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RecommendationScore that = (RecommendationScore) o;
        return Objects.equals(recommendedUser.getId(), that.recommendedUser.getId());
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(recommendedUser.getId());
    }
    
    @Override
    public String toString() {
        return "RecommendationScore{" +
                "user=" + recommendedUser.getUsername() +
                ", jaccardScore=" + String.format("%.4f", jaccardScore) +
                ", adamicAdarScore=" + String.format("%.4f", adamicAdarScore) +
                ", combinedScore=" + String.format("%.4f", combinedScore) +
                ", mutualFriends=" + mutualFriendsCount +
                ", algorithm='" + algorithm + '\'' +
                '}';
    }
}
