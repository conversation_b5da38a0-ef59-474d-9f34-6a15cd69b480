package edu.social.config;

import edu.social.batch.RecommendationBatchProcessor;
import edu.social.model.User;
import edu.social.repository.UserRepository;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.batch.item.data.builder.RepositoryItemReaderBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.Collections;

/**
 * Spring Batch configuration for precomputing friend recommendations
 */
@Configuration
public class BatchConfig {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private RecommendationBatchProcessor recommendationBatchProcessor;
    
    @Value("${social-network.recommendations.batch-size:1000}")
    private int batchSize;
    
    /**
     * Item reader for reading users from database
     */
    @Bean
    public ItemReader<User> userItemReader() {
        return new RepositoryItemReaderBuilder<User>()
                .name("userItemReader")
                .repository(userRepository)
                .methodName("findByIsActiveTrue")
                .pageSize(batchSize)
                .sorts(Collections.singletonMap("id", Sort.Direction.ASC))
                .build();
    }
    
    /**
     * Item processor for generating recommendations
     */
    @Bean
    public ItemProcessor<User, User> recommendationItemProcessor() {
        return recommendationBatchProcessor;
    }
    
    /**
     * Item writer for storing precomputed recommendations
     */
    @Bean
    public ItemWriter<User> recommendationItemWriter() {
        return users -> {
            // The actual writing is handled in the processor
            // This is just a pass-through writer
            users.forEach(user -> {
                // Log completion or perform any final operations
                System.out.println("Processed recommendations for user: " + user.getUsername());
            });
        };
    }
    
    /**
     * Step for processing recommendations
     */
    @Bean
    public Step recommendationStep(JobRepository jobRepository, 
                                  PlatformTransactionManager transactionManager) {
        return new StepBuilder("recommendationStep", jobRepository)
                .<User, User>chunk(10, transactionManager)
                .reader(userItemReader())
                .processor(recommendationItemProcessor())
                .writer(recommendationItemWriter())
                .build();
    }
    
    /**
     * Job for precomputing recommendations
     */
    @Bean
    public Job recommendationJob(JobRepository jobRepository, Step recommendationStep) {
        return new JobBuilder("recommendationJob", jobRepository)
                .start(recommendationStep)
                .build();
    }
}
