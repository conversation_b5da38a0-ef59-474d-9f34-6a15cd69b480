package edu.social.config;

import edu.social.service.FriendshipService;
import edu.social.service.SocialGraphService;
import edu.social.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Initializes sample data for testing the social network
 */
@Component
public class DataInitializer implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private FriendshipService friendshipService;
    
    @Autowired
    private SocialGraphService socialGraphService;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("Initializing sample data...");
        
        try {
            // Create sample users
            createSampleUsers();
            
            // Create sample friendships
            createSampleFriendships();
            
            // Build the social graph
            socialGraphService.buildGraph();
            
            logger.info("Sample data initialization completed successfully");
            
        } catch (Exception e) {
            logger.error("Error initializing sample data", e);
        }
    }
    
    private void createSampleUsers() {
        logger.info("Creating sample users...");
        
        // Create 20 sample users
        String[] usernames = {
            "alice", "bob", "charlie", "diana", "eve", "frank", "grace", "henry",
            "iris", "jack", "kate", "liam", "mia", "noah", "olivia", "peter",
            "quinn", "rachel", "sam", "tina"
        };
        
        String[] fullNames = {
            "Alice Johnson", "Bob Smith", "Charlie Brown", "Diana Prince", "Eve Adams",
            "Frank Miller", "Grace Lee", "Henry Wilson", "Iris Chen", "Jack Davis",
            "Kate Taylor", "Liam Anderson", "Mia Garcia", "Noah Martinez", "Olivia Rodriguez",
            "Peter Thompson", "Quinn White", "Rachel Harris", "Sam Clark", "Tina Lewis"
        };
        
        for (int i = 0; i < usernames.length; i++) {
            try {
                userService.createUser(
                    usernames[i],
                    usernames[i] + "@example.com",
                    fullNames[i]
                );
                logger.debug("Created user: {}", usernames[i]);
            } catch (IllegalArgumentException e) {
                // User might already exist, skip
                logger.debug("User {} already exists, skipping", usernames[i]);
            }
        }
        
        logger.info("Sample users created successfully");
    }
    
    private void createSampleFriendships() {
        logger.info("Creating sample friendships...");
        
        // Create a network with interesting patterns for recommendations
        // Alice (1) is friends with Bob (2), Charlie (3), Diana (4)
        createFriendshipSafely(1L, 2L);
        createFriendshipSafely(1L, 3L);
        createFriendshipSafely(1L, 4L);
        
        // Bob (2) is friends with Alice (1), Eve (5), Frank (6)
        createFriendshipSafely(2L, 5L);
        createFriendshipSafely(2L, 6L);
        
        // Charlie (3) is friends with Alice (1), Grace (7), Henry (8)
        createFriendshipSafely(3L, 7L);
        createFriendshipSafely(3L, 8L);
        
        // Diana (4) is friends with Alice (1), Iris (9), Jack (10)
        createFriendshipSafely(4L, 9L);
        createFriendshipSafely(4L, 10L);
        
        // Create some mutual connections for better recommendations
        // Eve (5) and Frank (6) are both friends with Bob (2), and also with each other
        createFriendshipSafely(5L, 6L);
        
        // Grace (7) and Henry (8) are both friends with Charlie (3), and also with each other
        createFriendshipSafely(7L, 8L);
        
        // Create a separate cluster
        createFriendshipSafely(11L, 12L); // Kate - Liam
        createFriendshipSafely(11L, 13L); // Kate - Mia
        createFriendshipSafely(12L, 13L); // Liam - Mia
        createFriendshipSafely(12L, 14L); // Liam - Noah
        createFriendshipSafely(13L, 15L); // Mia - Olivia
        
        // Connect the clusters through some bridge connections
        createFriendshipSafely(6L, 11L);  // Frank - Kate (bridge)
        createFriendshipSafely(8L, 14L);  // Henry - Noah (bridge)
        
        // Create some isolated friendships
        createFriendshipSafely(16L, 17L); // Peter - Quinn
        createFriendshipSafely(17L, 18L); // Quinn - Rachel
        createFriendshipSafely(18L, 19L); // Rachel - Sam
        createFriendshipSafely(19L, 20L); // Sam - Tina
        
        logger.info("Sample friendships created successfully");
    }
    
    private void createFriendshipSafely(Long userId1, Long userId2) {
        try {
            friendshipService.createFriendship(userId1, userId2);
            logger.debug("Created friendship between users {} and {}", userId1, userId2);
        } catch (Exception e) {
            logger.debug("Failed to create friendship between users {} and {}: {}", 
                        userId1, userId2, e.getMessage());
        }
    }
}
