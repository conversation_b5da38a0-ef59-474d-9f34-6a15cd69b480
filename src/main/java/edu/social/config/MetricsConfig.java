package edu.social.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for custom metrics
 */
@Configuration
public class MetricsConfig {
    
    @Bean
    public Timer recommendationTimer(MeterRegistry meterRegistry) {
        return Timer.builder("recommendation.generation.time")
                .description("Time taken to generate recommendations")
                .register(meterRegistry);
    }
    
    @Bean
    public Timer graphBuildTimer(MeterRegistry meterRegistry) {
        return Timer.builder("graph.build.time")
                .description("Time taken to build social graph")
                .register(meterRegistry);
    }
}
