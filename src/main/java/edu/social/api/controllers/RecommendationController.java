package edu.social.api.controllers;

import edu.social.model.RecommendationScore;
import edu.social.model.User;
import edu.social.service.RecommendationService;
import edu.social.service.SocialGraphService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST Controller for friend recommendation operations
 */
@RestController
@RequestMapping("/api/recommendations")
@CrossOrigin(origins = "*")
public class RecommendationController {
    
    private static final Logger logger = LoggerFactory.getLogger(RecommendationController.class);
    
    @Autowired
    private RecommendationService recommendationService;
    
    @Autowired
    private SocialGraphService socialGraphService;
    
    /**
     * Get friend recommendations for a user
     */
    @GetMapping("/users/{userId}")
    public ResponseEntity<?> getRecommendations(@PathVariable Long userId) {
        try {
            logger.info("Getting recommendations for user: {}", userId);
            
            List<RecommendationScore> recommendations = recommendationService.generateRecommendations(userId);
            return ResponseEntity.ok(recommendations);
            
        } catch (Exception e) {
            logger.error("Error getting recommendations for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get recommendations using only Jaccard similarity
     */
    @GetMapping("/users/{userId}/jaccard")
    public ResponseEntity<?> getJaccardRecommendations(@PathVariable Long userId) {
        try {
            logger.info("Getting Jaccard recommendations for user: {}", userId);
            
            List<RecommendationScore> recommendations = recommendationService.generateJaccardRecommendations(userId);
            return ResponseEntity.ok(recommendations);
            
        } catch (Exception e) {
            logger.error("Error getting Jaccard recommendations for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get recommendations using only Adamic-Adar index
     */
    @GetMapping("/users/{userId}/adamic-adar")
    public ResponseEntity<?> getAdamicAdarRecommendations(@PathVariable Long userId) {
        try {
            logger.info("Getting Adamic-Adar recommendations for user: {}", userId);
            
            List<RecommendationScore> recommendations = recommendationService.generateAdamicAdarRecommendations(userId);
            return ResponseEntity.ok(recommendations);
            
        } catch (Exception e) {
            logger.error("Error getting Adamic-Adar recommendations for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get mutual friends between two users
     */
    @GetMapping("/users/{userId1}/mutual/{userId2}")
    public ResponseEntity<?> getMutualFriends(@PathVariable Long userId1, @PathVariable Long userId2) {
        try {
            logger.info("Getting mutual friends between users {} and {}", userId1, userId2);
            
            List<User> mutualFriends = recommendationService.getMutualFriends(userId1, userId2);
            return ResponseEntity.ok(mutualFriends);
            
        } catch (Exception e) {
            logger.error("Error getting mutual friends between users {} and {}", userId1, userId2, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get recommendation statistics for a user
     */
    @GetMapping("/users/{userId}/stats")
    public ResponseEntity<?> getRecommendationStatistics(@PathVariable Long userId) {
        try {
            logger.info("Getting recommendation statistics for user: {}", userId);
            
            Map<String, Object> stats = recommendationService.getRecommendationStatistics(userId);
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            logger.error("Error getting recommendation statistics for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get similarity score between two users
     */
    @GetMapping("/similarity")
    public ResponseEntity<?> getSimilarityScore(@RequestParam Long userId1, 
                                               @RequestParam Long userId2,
                                               @RequestParam(defaultValue = "jaccard") String algorithm) {
        try {
            logger.info("Getting {} similarity between users {} and {}", algorithm, userId1, userId2);
            
            double score;
            switch (algorithm.toLowerCase()) {
                case "jaccard":
                    score = socialGraphService.calculateJaccardSimilarity(userId1, userId2);
                    break;
                case "adamic-adar":
                    score = socialGraphService.calculateAdamicAdarIndex(userId1, userId2);
                    break;
                default:
                    return ResponseEntity.badRequest()
                            .body(new ErrorResponse("Unsupported algorithm: " + algorithm));
            }
            
            return ResponseEntity.ok(new SimilarityResponse(userId1, userId2, algorithm, score));
            
        } catch (Exception e) {
            logger.error("Error calculating similarity between users {} and {}", userId1, userId2, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get graph statistics
     */
    @GetMapping("/graph/stats")
    public ResponseEntity<?> getGraphStatistics() {
        try {
            logger.info("Getting graph statistics");
            
            Map<String, Object> stats = socialGraphService.getGraphStatistics();
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            logger.error("Error getting graph statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Rebuild the social graph
     */
    @PostMapping("/graph/rebuild")
    public ResponseEntity<?> rebuildGraph() {
        try {
            logger.info("Rebuilding social graph");
            
            socialGraphService.buildGraph();
            return ResponseEntity.ok(new MessageResponse("Graph rebuilt successfully"));
            
        } catch (Exception e) {
            logger.error("Error rebuilding graph", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    // Response DTOs
    public static class SimilarityResponse {
        private Long userId1;
        private Long userId2;
        private String algorithm;
        private double score;
        
        public SimilarityResponse(Long userId1, Long userId2, String algorithm, double score) {
            this.userId1 = userId1;
            this.userId2 = userId2;
            this.algorithm = algorithm;
            this.score = score;
        }
        
        // Getters
        public Long getUserId1() { return userId1; }
        public Long getUserId2() { return userId2; }
        public String getAlgorithm() { return algorithm; }
        public double getScore() { return score; }
    }
    
    public static class MessageResponse {
        private String message;
        
        public MessageResponse(String message) { this.message = message; }
        public String getMessage() { return message; }
    }
    
    public static class ErrorResponse {
        private String message;
        
        public ErrorResponse(String message) { this.message = message; }
        public String getMessage() { return message; }
    }
}
