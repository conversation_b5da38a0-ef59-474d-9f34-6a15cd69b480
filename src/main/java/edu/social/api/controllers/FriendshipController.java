package edu.social.api.controllers;

import edu.social.model.Friendship;
import edu.social.model.User;
import edu.social.service.FriendshipService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for friendship management operations
 */
@RestController
@RequestMapping("/api/friendships")
@CrossOrigin(origins = "*")
public class FriendshipController {
    
    private static final Logger logger = LoggerFactory.getLogger(FriendshipController.class);
    
    @Autowired
    private FriendshipService friendshipService;
    
    /**
     * Create friendship between two users
     */
    @PostMapping
    public ResponseEntity<?> createFriendship(@RequestBody CreateFriendshipRequest request) {
        try {
            logger.info("Creating friendship between users {} and {}", 
                       request.getUserId1(), request.getUserId2());
            
            Friendship friendship = friendshipService.createFriendship(
                request.getUserId1(), 
                request.getUserId2()
            );
            
            return ResponseEntity.status(HttpStatus.CREATED).body(friendship);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Failed to create friendship: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse(e.getMessage()));
        } catch (Exception e) {
            logger.error("Error creating friendship", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Remove friendship between two users
     */
    @DeleteMapping
    public ResponseEntity<?> removeFriendship(@RequestBody RemoveFriendshipRequest request) {
        try {
            logger.info("Removing friendship between users {} and {}", 
                       request.getUserId1(), request.getUserId2());
            
            friendshipService.removeFriendship(request.getUserId1(), request.getUserId2());
            return ResponseEntity.noContent().build();
            
        } catch (IllegalArgumentException e) {
            logger.warn("Failed to remove friendship: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse(e.getMessage()));
        } catch (Exception e) {
            logger.error("Error removing friendship", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Block a user
     */
    @PostMapping("/block")
    public ResponseEntity<?> blockUser(@RequestBody BlockUserRequest request) {
        try {
            logger.info("User {} blocking user {}", request.getUserId(), request.getUserToBlockId());
            
            friendshipService.blockUser(request.getUserId(), request.getUserToBlockId());
            return ResponseEntity.ok().build();
            
        } catch (IllegalArgumentException e) {
            logger.warn("Failed to block user: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse(e.getMessage()));
        } catch (Exception e) {
            logger.error("Error blocking user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Unblock a user
     */
    @PostMapping("/unblock")
    public ResponseEntity<?> unblockUser(@RequestBody UnblockUserRequest request) {
        try {
            logger.info("User {} unblocking user {}", request.getUserId(), request.getUserToUnblockId());
            
            friendshipService.unblockUser(request.getUserId(), request.getUserToUnblockId());
            return ResponseEntity.ok().build();
            
        } catch (IllegalArgumentException e) {
            logger.warn("Failed to unblock user: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse(e.getMessage()));
        } catch (Exception e) {
            logger.error("Error unblocking user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Check if two users are friends
     */
    @GetMapping("/check")
    public ResponseEntity<?> checkFriendship(@RequestParam Long userId1, @RequestParam Long userId2) {
        try {
            boolean areFriends = friendshipService.areFriends(userId1, userId2);
            return ResponseEntity.ok(new FriendshipStatusResponse(areFriends));
            
        } catch (Exception e) {
            logger.error("Error checking friendship between users {} and {}", userId1, userId2, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Check if a user is blocked
     */
    @GetMapping("/blocked")
    public ResponseEntity<?> checkBlocked(@RequestParam Long userId, @RequestParam Long potentialBlockedUserId) {
        try {
            boolean isBlocked = friendshipService.isBlocked(userId, potentialBlockedUserId);
            return ResponseEntity.ok(new BlockStatusResponse(isBlocked));
            
        } catch (Exception e) {
            logger.error("Error checking if user {} is blocked by user {}", 
                        potentialBlockedUserId, userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get active friends of a user
     */
    @GetMapping("/{userId}/friends")
    public ResponseEntity<?> getActiveFriends(@PathVariable Long userId) {
        try {
            List<User> friends = friendshipService.getActiveFriends(userId);
            return ResponseEntity.ok(friends);
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("Error getting active friends for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get friendship count for a user
     */
    @GetMapping("/{userId}/count")
    public ResponseEntity<?> getFriendshipCount(@PathVariable Long userId) {
        try {
            long count = friendshipService.getFriendshipCount(userId);
            return ResponseEntity.ok(new FriendshipCountResponse(count));
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("Error getting friendship count for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    /**
     * Get all friendships for a user
     */
    @GetMapping("/{userId}/all")
    public ResponseEntity<?> getAllFriendships(@PathVariable Long userId) {
        try {
            List<Friendship> friendships = friendshipService.getAllFriendships(userId);
            return ResponseEntity.ok(friendships);
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("Error getting all friendships for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Internal server error"));
        }
    }
    
    // Request/Response DTOs
    public static class CreateFriendshipRequest {
        private Long userId1;
        private Long userId2;
        
        public Long getUserId1() { return userId1; }
        public void setUserId1(Long userId1) { this.userId1 = userId1; }
        public Long getUserId2() { return userId2; }
        public void setUserId2(Long userId2) { this.userId2 = userId2; }
    }
    
    public static class RemoveFriendshipRequest {
        private Long userId1;
        private Long userId2;
        
        public Long getUserId1() { return userId1; }
        public void setUserId1(Long userId1) { this.userId1 = userId1; }
        public Long getUserId2() { return userId2; }
        public void setUserId2(Long userId2) { this.userId2 = userId2; }
    }
    
    public static class BlockUserRequest {
        private Long userId;
        private Long userToBlockId;
        
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public Long getUserToBlockId() { return userToBlockId; }
        public void setUserToBlockId(Long userToBlockId) { this.userToBlockId = userToBlockId; }
    }
    
    public static class UnblockUserRequest {
        private Long userId;
        private Long userToUnblockId;
        
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public Long getUserToUnblockId() { return userToUnblockId; }
        public void setUserToUnblockId(Long userToUnblockId) { this.userToUnblockId = userToUnblockId; }
    }
    
    public static class FriendshipStatusResponse {
        private boolean areFriends;
        
        public FriendshipStatusResponse(boolean areFriends) { this.areFriends = areFriends; }
        public boolean isAreFriends() { return areFriends; }
    }
    
    public static class BlockStatusResponse {
        private boolean isBlocked;
        
        public BlockStatusResponse(boolean isBlocked) { this.isBlocked = isBlocked; }
        public boolean isBlocked() { return isBlocked; }
    }
    
    public static class FriendshipCountResponse {
        private long count;
        
        public FriendshipCountResponse(long count) { this.count = count; }
        public long getCount() { return count; }
    }
    
    public static class ErrorResponse {
        private String message;
        
        public ErrorResponse(String message) { this.message = message; }
        public String getMessage() { return message; }
    }
}
