package edu.social.service;

import edu.social.model.User;
import edu.social.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SocialGraphServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private SocialGraphService socialGraphService;
    
    private List<User> testUsers;
    
    @BeforeEach
    void setUp() {
        testUsers = createTestUsers();
    }
    
    @Test
    void testBuildGraph() {
        // Given
        when(userRepository.findByIsActiveTrue()).thenReturn(testUsers);
        
        // When
        socialGraphService.buildGraph();
        
        // Then
        Set<Long> user1Friends = socialGraphService.getDirectFriends(1L);
        assertTrue(user1Friends.contains(2L));
        assertTrue(user1Friends.contains(3L));
        assertEquals(2, user1Friends.size());
        
        verify(userRepository).findByIsActiveTrue();
    }
    
    @Test
    void testGetTwoHopNeighborhood() {
        // Given
        when(userRepository.findByIsActiveTrue()).thenReturn(testUsers);
        socialGraphService.buildGraph();
        
        // When
        Set<Long> twoHopNeighbors = socialGraphService.getTwoHopNeighborhood(1L);
        
        // Then
        assertTrue(twoHopNeighbors.contains(4L)); // Friend of friend through user 2
        assertFalse(twoHopNeighbors.contains(2L)); // Direct friend, not two-hop
        assertFalse(twoHopNeighbors.contains(1L)); // Self
    }
    
    @Test
    void testCalculateJaccardSimilarity() {
        // Given
        when(userRepository.findByIsActiveTrue()).thenReturn(testUsers);
        socialGraphService.buildGraph();
        
        // When
        double similarity = socialGraphService.calculateJaccardSimilarity(1L, 2L);
        
        // Then
        assertTrue(similarity >= 0.0 && similarity <= 1.0);
    }
    
    @Test
    void testCalculateAdamicAdarIndex() {
        // Given
        when(userRepository.findByIsActiveTrue()).thenReturn(testUsers);
        socialGraphService.buildGraph();
        
        // When
        double adamicAdar = socialGraphService.calculateAdamicAdarIndex(1L, 4L);
        
        // Then
        assertTrue(adamicAdar >= 0.0);
    }
    
    @Test
    void testGetMutualFriends() {
        // Given
        when(userRepository.findByIsActiveTrue()).thenReturn(testUsers);
        socialGraphService.buildGraph();
        
        // When
        Set<Long> mutualFriends = socialGraphService.getMutualFriends(1L, 4L);
        
        // Then
        assertTrue(mutualFriends.contains(2L)); // User 2 is friend of both 1 and 4
    }
    
    @Test
    void testAddFriendship() {
        // Given
        when(userRepository.findByIsActiveTrue()).thenReturn(testUsers);
        socialGraphService.buildGraph();
        
        // When
        socialGraphService.addFriendship(1L, 5L);
        
        // Then
        Set<Long> user1Friends = socialGraphService.getDirectFriends(1L);
        Set<Long> user5Friends = socialGraphService.getDirectFriends(5L);
        
        assertTrue(user1Friends.contains(5L));
        assertTrue(user5Friends.contains(1L));
    }
    
    @Test
    void testRemoveFriendship() {
        // Given
        when(userRepository.findByIsActiveTrue()).thenReturn(testUsers);
        socialGraphService.buildGraph();
        
        // When
        socialGraphService.removeFriendship(1L, 2L);
        
        // Then
        Set<Long> user1Friends = socialGraphService.getDirectFriends(1L);
        Set<Long> user2Friends = socialGraphService.getDirectFriends(2L);
        
        assertFalse(user1Friends.contains(2L));
        assertFalse(user2Friends.contains(1L));
    }
    
    private List<User> createTestUsers() {
        List<User> users = new ArrayList<>();
        
        // Create 5 test users
        for (int i = 1; i <= 5; i++) {
            User user = new User("user" + i, "user" + i + "@test.com", "User " + i);
            user.setId((long) i);
            users.add(user);
        }
        
        // Set up friendships
        // User 1 friends with User 2, User 3
        users.get(0).getFriends().add(users.get(1));
        users.get(0).getFriends().add(users.get(2));
        
        // User 2 friends with User 1, User 4
        users.get(1).getFriends().add(users.get(0));
        users.get(1).getFriends().add(users.get(3));
        
        // User 3 friends with User 1
        users.get(2).getFriends().add(users.get(0));
        
        // User 4 friends with User 2
        users.get(3).getFriends().add(users.get(1));
        
        // User 5 has no friends initially
        
        return users;
    }
}
