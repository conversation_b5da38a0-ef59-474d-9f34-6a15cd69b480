package edu.social.api.controllers;

import edu.social.model.RecommendationScore;
import edu.social.model.User;
import edu.social.service.RecommendationService;
import edu.social.service.SocialGraphService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(RecommendationController.class)
class RecommendationControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private RecommendationService recommendationService;
    
    @MockBean
    private SocialGraphService socialGraphService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void testGetRecommendations() throws Exception {
        // Given
        User recommendedUser = new User("testuser", "<EMAIL>", "Test User");
        recommendedUser.setId(2L);
        
        RecommendationScore score = new RecommendationScore(
            recommendedUser, 0.5, 0.7, 3, "COMBINED_SIMILARITY"
        );
        
        List<RecommendationScore> recommendations = Arrays.asList(score);
        
        when(recommendationService.generateRecommendations(1L)).thenReturn(recommendations);
        
        // When & Then
        mockMvc.perform(get("/api/recommendations/users/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].recommendedUser.username").value("testuser"))
                .andExpect(jsonPath("$[0].jaccardScore").value(0.5))
                .andExpect(jsonPath("$[0].adamicAdarScore").value(0.7))
                .andExpect(jsonPath("$[0].mutualFriendsCount").value(3));
    }
    
    @Test
    void testGetJaccardRecommendations() throws Exception {
        // Given
        User recommendedUser = new User("testuser", "<EMAIL>", "Test User");
        recommendedUser.setId(2L);
        
        RecommendationScore score = new RecommendationScore(
            recommendedUser, 0.6, 0.0, 2, "JACCARD"
        );
        
        List<RecommendationScore> recommendations = Arrays.asList(score);
        
        when(recommendationService.generateJaccardRecommendations(1L)).thenReturn(recommendations);
        
        // When & Then
        mockMvc.perform(get("/api/recommendations/users/1/jaccard"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$[0].algorithm").value("JACCARD"));
    }
    
    @Test
    void testGetMutualFriends() throws Exception {
        // Given
        User mutualFriend = new User("mutual", "<EMAIL>", "Mutual Friend");
        mutualFriend.setId(3L);
        
        List<User> mutualFriends = Arrays.asList(mutualFriend);
        
        when(recommendationService.getMutualFriends(1L, 2L)).thenReturn(mutualFriends);
        
        // When & Then
        mockMvc.perform(get("/api/recommendations/users/1/mutual/2"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$[0].username").value("mutual"));
    }
    
    @Test
    void testGetRecommendationStatistics() throws Exception {
        // Given
        Map<String, Object> stats = new HashMap<>();
        stats.put("directFriendsCount", 5);
        stats.put("twoHopNeighborsCount", 15);
        stats.put("potentialRecommendations", 10);
        
        when(recommendationService.getRecommendationStatistics(1L)).thenReturn(stats);
        
        // When & Then
        mockMvc.perform(get("/api/recommendations/users/1/stats"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.directFriendsCount").value(5))
                .andExpect(jsonPath("$.twoHopNeighborsCount").value(15))
                .andExpect(jsonPath("$.potentialRecommendations").value(10));
    }
    
    @Test
    void testGetSimilarityScore() throws Exception {
        // Given
        when(socialGraphService.calculateJaccardSimilarity(1L, 2L)).thenReturn(0.75);
        
        // When & Then
        mockMvc.perform(get("/api/recommendations/similarity")
                .param("userId1", "1")
                .param("userId2", "2")
                .param("algorithm", "jaccard"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.userId1").value(1))
                .andExpect(jsonPath("$.userId2").value(2))
                .andExpect(jsonPath("$.algorithm").value("jaccard"))
                .andExpect(jsonPath("$.score").value(0.75));
    }
    
    @Test
    void testGetGraphStatistics() throws Exception {
        // Given
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalUsers", 100);
        stats.put("totalConnections", 250);
        stats.put("averageDegree", 5.0);
        
        when(socialGraphService.getGraphStatistics()).thenReturn(stats);
        
        // When & Then
        mockMvc.perform(get("/api/recommendations/graph/stats"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.totalUsers").value(100))
                .andExpect(jsonPath("$.totalConnections").value(250))
                .andExpect(jsonPath("$.averageDegree").value(5.0));
    }
    
    @Test
    void testRebuildGraph() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/recommendations/graph/rebuild"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Graph rebuilt successfully"));
    }
}
